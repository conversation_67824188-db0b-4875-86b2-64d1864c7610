<?php
/**
 * 测试DeepSeek模拟API
 */

$url = 'https://aiapi.tiptop.cn/deepseek/chat/completions';
$data = [
    'model' => 'deepseek-chat',
    'messages' => [
        [
            'role' => 'user',
            'content' => '请写一首关于夏天的诗'
        ]
    ]
];

// 使用curl发送请求
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'User-Agent: TestScript/1.0'
    ],
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_SSL_VERIFYPEER => false, // 忽略SSL验证（仅测试用）
    CURLOPT_SSL_VERIFYHOST => false
]);

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($result === FALSE || !empty($error)) {
    echo "请求失败\n";
    echo "CURL错误: " . $error . "\n";
    echo "HTTP状态码: " . $httpCode . "\n";
    echo "URL: " . $url . "\n";
} else {
    echo "响应结果：\n";
    echo $result . "\n";
    
    // 解析JSON响应
    $response = json_decode($result, true);
    if ($response && isset($response['choices'][0]['message']['content'])) {
        echo "\n生成的内容：\n";
        echo $response['choices'][0]['message']['content'] . "\n";
    }
}
