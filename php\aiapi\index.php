<?php
/**
 * 模拟AI的API服务 - 入口文件和路由分发
 * 根据第三方AI的API接口文档模拟接收和返回处理
 * 支持后续无缝切换到真实AI平台API接口
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 设置执行时间限制以支持长时间AI任务（如视频生成）
set_time_limit(600); // 10分钟，足够处理最长的视频生成任务
ini_set('max_execution_time', 600);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 自动加载类文件
spl_autoload_register(function ($className) {
    $directories = [
        __DIR__ . '/controllers/',
        __DIR__ . '/models/',
        __DIR__ . '/utils/'
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $className . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// 加载配置文件
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/routes.php';

// 注册错误处理器
ErrorHandler::register();

// 初始化性能监控
$performanceMonitor = PerformanceMonitor::getInstance();
$performanceMonitor->startTimer('total_request');

// 获取请求信息
$requestMethod = $_SERVER['REQUEST_METHOD'];
$requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// 智能路径处理：支持简洁URL和完整URL
if (strpos($requestUri, '/php/aiapi') === 0) {
    // 完整URL路径：/php/aiapi/config -> /config
    $requestPath = str_replace('/php/aiapi', '', $requestUri);
} else {
    // 简洁URL路径：/config -> /config（直接使用）
    $requestPath = $requestUri;
}

// 清理路径：移除末尾斜杠，确保路径格式一致
$requestPath = rtrim($requestPath, '/');

// 初始化日志记录器
$logger = new Logger();
$logger->info("API请求", [
    'method' => $requestMethod,
    'path' => $requestPath,
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
]);

try {
    // 路由分发
    $router = new Router();
    $response = $router->dispatch($requestMethod, $requestPath);
    
    // 输出响应
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

    // 结束总体计时
    $totalTiming = $performanceMonitor->endTimer('total_request');

    // 检查性能阈值
    if ($totalTiming) {
        $performanceMonitor->checkPerformanceThresholds(
            $totalTiming['duration'] * 1000,
            $totalTiming['memory_used']
        );
    }
    
} catch (Exception $e) {
    // 错误处理
    $logger->error("API错误", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    
    http_response_code(500);
    echo json_encode([
        'error' => [
            'code' => 'INTERNAL_ERROR',
            'message' => '服务器内部错误',
            'details' => AIAPI_DEBUG ? $e->getMessage() : null
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 简单路由器类
 */
class Router
{
    private $routes = [];
    
    public function __construct()
    {
        $this->loadRoutes();
    }
    
    private function loadRoutes()
    {
        // 使用正确的全局变量名
        $this->routes = $GLOBALS['aiapi_routes'] ?? [];

        // 调试：输出路由数量
        error_log("Loaded " . count($this->routes) . " routes");

        // 调试：输出前几个路由
        foreach (array_slice($this->routes, 0, 3) as $route) {
            error_log("Route: " . $route['method'] . " " . $route['path'] . " -> " . $route['controller'] . "@" . $route['action']);
        }
    }
    
    public function dispatch($method, $path)
    {
        // 移除开头的斜杠
        $path = ltrim($path, '/');

        // 获取性能监控实例
        $performanceMonitor = PerformanceMonitor::getInstance();

        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchPath($route['path'], $path)) {
                try {
                    // 开始计时
                    $performanceMonitor->startTimer('controller_execution');

                    $controller = new $route['controller']();
                    $action = $route['action'];

                    // 提取路径参数
                    $params = $this->extractParams($route['path'], $path);

                    // 执行控制器方法
                    $result = $controller->$action($params);

                    // 结束计时
                    $timing = $performanceMonitor->endTimer('controller_execution');

                    // 记录性能指标
                    $platform = $this->getPlatformFromController($route['controller']);
                    $performanceMonitor->recordApiRequest(
                        $platform,
                        $route['path'],
                        $method,
                        $timing['duration'] * 1000, // 转换为毫秒
                        $timing['memory_used']
                    );

                    return $result;

                } catch (Exception $e) {
                    // 使用统一错误处理器
                    $errorHandler = ErrorHandler::getInstance();
                    $platform = $this->getPlatformFromController($route['controller']);

                    return $errorHandler->handleException($e, $platform);
                }
            }
        }

        // 404 未找到
        http_response_code(404);
        return [
            'error' => [
                'code' => 'NOT_FOUND',
                'message' => '接口不存在',
                'path' => $path
            ]
        ];
    }
    
    private function matchPath($routePath, $requestPath)
    {
        $routePattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $routePath);
        return preg_match('#^' . $routePattern . '$#', $requestPath);
    }
    
    private function extractParams($routePath, $requestPath)
    {
        $params = [];
        $routeParts = explode('/', $routePath);
        $requestParts = explode('/', $requestPath);
        
        for ($i = 0; $i < count($routeParts); $i++) {
            if (isset($routeParts[$i]) && preg_match('/\{([^}]+)\}/', $routeParts[$i], $matches)) {
                $paramName = $matches[1];
                $params[$paramName] = $requestParts[$i] ?? null;
            }
        }
        
        return $params;
    }

    /**
     * 根据控制器名称获取平台标识
     */
    private function getPlatformFromController($controllerClass)
    {
        if (strpos($controllerClass, 'DeepSeek') !== false) {
            return 'deepseek';
        } elseif (strpos($controllerClass, 'Liblib') !== false) {
            return 'liblib';
        } elseif (strpos($controllerClass, 'Kling') !== false) {
            return 'kling';
        } elseif (strpos($controllerClass, 'MiniMax') !== false) {
            return 'minimax';
        } elseif (strpos($controllerClass, 'Workflow') !== false) {
            return 'workflow';
        } elseif (strpos($controllerClass, 'System') !== false) {
            return 'system';
        } else {
            return 'unknown';
        }
    }
}
